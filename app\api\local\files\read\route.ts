import { NextRequest } from 'next/server'
import { readFile, stat } from 'fs/promises'
import { resolve, extname } from 'path'

// Helper function to check if a file is likely binary
function isBinaryFile(filePath: string): boolean {
  const binaryExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.zip', '.rar', '.7z', '.tar', '.gz',
    '.exe', '.dll', '.so', '.dylib',
    '.mp3', '.mp4', '.avi', '.mov', '.wmv',
    '.ttf', '.otf', '.woff', '.woff2'
  ];

  const ext = extname(filePath).toLowerCase();
  return binaryExtensions.includes(ext);
}

export async function POST(req: NextRequest) {
  try {
    const { path } = await req.json()

    // Get the workspace root directory
    const workspaceRoot = process.cwd()

    // Resolve the requested path relative to workspace root
    const requestedPath = resolve(workspaceRoot, path)

    // Security check: ensure the path is within the workspace
    if (!requestedPath.startsWith(workspaceRoot)) {
      return new Response(JSON.stringify({
        error: 'Access denied: Path outside workspace',
        content: ''
      }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    // Check if file is binary
    if (isBinaryFile(requestedPath)) {
      const stats = await stat(requestedPath)
      return new Response(JSON.stringify({
        content: `[Binary file - ${extname(requestedPath)} - ${(stats.size / 1024).toFixed(2)} KB]\n\nThis is a binary file and cannot be displayed as text.`,
        isBinary: true
      }), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    const content = await readFile(requestedPath, 'utf-8')

    return new Response(JSON.stringify({
      content,
      isBinary: false
    }), {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('Local file read error:', error)
    return new Response(JSON.stringify({
      error: `Error reading local file: ${error instanceof Error ? error.message : String(error)}`,
      content: ''
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
