import { SVGProps } from 'react'

interface LogoProps extends SVGProps<SVGSVGElement> {
  variant?: 'e2b' | 'default'
}

export default function Logo({ variant = 'default', ...props }: LogoProps) {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17 7L7 17M7 7L17 17"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}