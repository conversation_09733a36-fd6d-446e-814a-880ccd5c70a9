import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { PlanSchema } from '@/lib/schema'
import { CheckCircle, Edit3, Clock, Zap, Target } from 'lucide-react'

interface PlanningWidgetProps {
  plan: PlanSchema
  onAccept: () => void
  onEdit: (editedPlan: PlanSchema) => void
  isLoading?: boolean
}

export function PlanningWidget({ plan, onAccept, onEdit, isLoading }: PlanningWidgetProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedPlan, setEditedPlan] = useState<PlanSchema>(plan)

  const handleSaveEdit = () => {
    onEdit(editedPlan)
    setIsEditing(false)
  }

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'complex': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getComplexityIcon = (complexity: string) => {
    switch (complexity) {
      case 'simple': return <Zap className="h-4 w-4" />
      case 'medium': return <Clock className="h-4 w-4" />
      case 'complex': return <Target className="h-4 w-4" />
      default: return null
    }
  }

  if (isEditing) {
    return (
      <Card className="w-full max-w-2xl mx-auto border-2 border-blue-200 shadow-lg">
        <CardHeader className="bg-blue-50">
          <CardTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5 text-blue-600" />
            Edit Project Plan
          </CardTitle>
          <CardDescription>
            Modify the plan details to better match your requirements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 p-6">
          <div className="space-y-2">
            <Label htmlFor="title">Project Title</Label>
            <Input
              id="title"
              value={editedPlan.title}
              onChange={(e) => setEditedPlan({ ...editedPlan, title: e.target.value })}
              placeholder="Enter project title (max 3 words)"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={editedPlan.description}
              onChange={(e) => setEditedPlan({ ...editedPlan, description: e.target.value })}
              placeholder="Brief description of the project"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="features">Features (one per line)</Label>
            <Textarea
              id="features"
              value={editedPlan.features.join('\n')}
              onChange={(e) => setEditedPlan({ 
                ...editedPlan, 
                features: e.target.value.split('\n').filter(f => f.trim()) 
              })}
              placeholder="List key features, one per line"
              rows={4}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="technical">Technical Approach</Label>
            <Textarea
              id="technical"
              value={editedPlan.technical_approach}
              onChange={(e) => setEditedPlan({ ...editedPlan, technical_approach: e.target.value })}
              placeholder="Technical approach and architecture overview"
              rows={3}
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button onClick={handleSaveEdit} className="flex-1">
              Save Changes
            </Button>
            <Button variant="outline" onClick={() => setIsEditing(false)} className="flex-1">
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-2xl mx-auto border-2 border-green-200 shadow-lg">
      <CardHeader className="bg-green-50">
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          Project Plan Ready
        </CardTitle>
        <CardDescription>
          Review the generated plan and proceed to the next step
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 p-6">
        <div>
          <h3 className="font-semibold text-lg mb-2">{plan.title}</h3>
          <p className="text-gray-600 mb-4">{plan.description}</p>
        </div>

        <div>
          <h4 className="font-medium mb-2">Key Features:</h4>
          <ul className="space-y-1">
            {plan.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                <span className="text-sm">{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h4 className="font-medium mb-2">Technical Approach:</h4>
          <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
            {plan.technical_approach}
          </p>
        </div>

        <div className="flex items-center gap-4">
          <div>
            <span className="text-sm font-medium">Template: </span>
            <Badge variant="outline">{plan.template}</Badge>
          </div>
          <div>
            <span className="text-sm font-medium">Complexity: </span>
            <Badge className={getComplexityColor(plan.estimated_complexity)}>
              <span className="flex items-center gap-1">
                {getComplexityIcon(plan.estimated_complexity)}
                {plan.estimated_complexity}
              </span>
            </Badge>
          </div>
        </div>

        <div className="flex gap-3 pt-4">
          <Button 
            onClick={onAccept} 
            className="flex-1 bg-green-600 hover:bg-green-700"
            disabled={isLoading}
          >
            {isLoading ? 'Processing...' : 'Accept Plan'}
          </Button>
          <Button 
            variant="outline" 
            onClick={() => setIsEditing(true)} 
            className="flex-1"
            disabled={isLoading}
          >
            <Edit3 className="h-4 w-4 mr-2" />
            Edit Plan
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
