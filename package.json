{"name": "frmwrk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/anthropic": "^1.1.6", "@ai-sdk/fireworks": "^0.2.6", "@ai-sdk/google": "^1.2.3", "@ai-sdk/google-vertex": "^2.2.3", "@ai-sdk/mistral": "^1.0.1", "@ai-sdk/openai": "^1.3.15", "@e2b/code-interpreter": "^1.5.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/react-visually-hidden": "^1.1.0", "@supabase/supabase-js": "^2.45.1", "@upstash/ratelimit": "^2.0.1", "@vercel/analytics": "^1.5.0", "@vercel/kv": "^2.0.0", "ai": "^3.3.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "core-js": "^3.38.0", "lucide-react": "^0.396.0", "next": "^14.2.28", "next-themes": "^0.3.0", "ollama-ai-provider": "^1.2.0", "posthog-js": "^1.158.3", "prismjs": "^1.30.0", "react": "^18", "react-dom": "^18", "react-textarea-autosize": "^8.5.3", "shadcn": "^2.6.0", "simple-icons": "^14.12.3", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "zod": "^3.23.8"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/node": "22.15.29", "@types/prismjs": "^1.26.4", "@types/react": "19.1.6", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}}