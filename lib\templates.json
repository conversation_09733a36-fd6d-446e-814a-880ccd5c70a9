{"nextjs-developer": {"name": "Next.js developer", "lib": ["nextjs@14.2.5", "typescript", "@types/node", "@types/react", "@types/react-dom", "postcss", "tailwindcss", "shadcn"], "file": "pages/index.tsx", "instructions": "Build modern, performant Next.js applications with elegant UI/UX. Utilize TailwindCSS and shadcn for polished interfaces. Focus on responsive design and component reusability. You can create multiple files and organize them in proper directory structure.", "port": 3000}, "vue-developer": {"name": "Vue.js developer", "lib": ["vue@latest", "nuxt@3.13.0", "tailwindcss"], "file": "app.vue", "instructions": "Create elegant Vue.js applications with a focus on component composition and state management. Implement responsive layouts and smooth animations. You can create multiple files and organize them in proper directory structure.", "port": 3000}}