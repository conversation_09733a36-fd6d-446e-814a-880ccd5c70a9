import { TemplateId } from './templates'
import { ExecutionError, Result } from '@e2b/code-interpreter'
import { PlanSchema, PreDesignVariantsSchema, PreDesignSchema } from './schema'

type ExecutionResultBase = {
  sbxId: string
}

export type ExecutionResultWeb = ExecutionResultBase & {
  template: TemplateId
  url: string
  terminalOutput?: string[]
}

export type ExecutionResult = ExecutionResultWeb

// Workflow stages
export type WorkflowStage = 'planning' | 'predesign' | 'coding' | 'complete'

export type WorkflowState = {
  stage: WorkflowStage
  plan?: PlanSchema
  preDesignVariants?: PreDesignVariantsSchema
  selectedPreDesign?: PreDesignSchema
  finalResult?: ExecutionResult
}
