import { NextRequest } from 'next/server'
import { readdir, stat } from 'fs/promises'
import { join, resolve } from 'path'

export async function POST(req: NextRequest) {
  try {
    const { path } = await req.json()
    
    // Get the workspace root directory
    const workspaceRoot = process.cwd()
    
    // Resolve the requested path relative to workspace root
    const requestedPath = path === '.' ? workspaceRoot : resolve(workspaceRoot, path)
    
    // Security check: ensure the path is within the workspace
    if (!requestedPath.startsWith(workspaceRoot)) {
      return new Response(JSON.stringify({
        error: 'Access denied: Path outside workspace',
        files: []
      }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    const files = await readdir(requestedPath, { withFileTypes: true })
    
    const fileEntries = await Promise.all(
      files
        .filter(file => !file.name.startsWith('.') && file.name !== 'node_modules')
        .map(async (file) => {
          const fullPath = join(requestedPath, file.name)
          const relativePath = path === '.' ? file.name : join(path, file.name)
          const stats = await stat(fullPath)
          
          return {
            path: relativePath,
            name: file.name,
            type: file.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime.toISOString()
          }
        })
    )

    // Sort directories first, then files, both alphabetically
    fileEntries.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1
      }
      return a.name.localeCompare(b.name)
    })

    return new Response(JSON.stringify({
      files: fileEntries
    }), {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('Local file system error:', error)
    return new Response(JSON.stringify({
      error: `Error accessing local file system: ${error instanceof Error ? error.message : String(error)}`,
      files: []
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
