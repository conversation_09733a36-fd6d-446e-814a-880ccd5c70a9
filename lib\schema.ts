import { z } from 'zod'

// Planning stage schema
export const planSchema = z.object({
  title: z.string().describe('Short title for the project. Max 3 words.'),
  description: z.string().describe('Brief description of what will be built. Max 2 sentences.'),
  features: z.array(z.string()).describe('List of key features to implement.'),
  technical_approach: z.string().describe('Technical approach and architecture overview.'),
  template: z.string().describe('Best template to use for this project.'),
  estimated_complexity: z.enum(['simple', 'medium', 'complex']).describe('Estimated complexity level.'),
})

export type PlanSchema = z.infer<typeof planSchema>

// PreDesign stage schema
export const preDesignSchema = z.object({
  variant_name: z.string().describe('Name of this design variant (e.g., "Modern Minimal", "Bold & Colorful").'),
  design_description: z.string().describe('Description of the design approach and visual style.'),
  html_content: z.string().describe('Complete HTML content for the mockup including CSS and JavaScript in a single file.'),
  key_features: z.array(z.string()).describe('Key visual and interactive features of this variant.'),
})

export type PreDesignSchema = z.infer<typeof preDesignSchema>

// Multiple PreDesign variants
export const preDesignVariantsSchema = z.object({
  variants: z.array(preDesignSchema).length(3).describe('Exactly 3 design variants to choose from.'),
  design_notes: z.string().describe('General notes about the design approach and considerations.'),
})

export type PreDesignVariantsSchema = z.infer<typeof preDesignVariantsSchema>

export const fragmentSchema = z.object({
  commentary: z.string().describe(`Describe what you're about to do and the steps you want to take for generating the fragment in great detail.`),
  template: z.string().describe('Name of the template used to generate the fragment.'),
  // template_ready: z.boolean().describe('Detect if finished identifying the template.'),
  title: z.string().describe('Short title of the fragment. Max 3 words.'),
  description: z.string().describe('Short description of the fragment. Max 1 sentence.'),
  additional_dependencies: z.array(z.string()).describe('Additional dependencies required by the fragment. Do not include dependencies that are already included in the template.'),
  has_additional_dependencies: z.boolean().describe('Detect if additional dependencies that are not included in the template are required by the fragment.'),
  install_dependencies_command: z.string().describe('Command to install additional dependencies required by the fragment.'),
  // install_dependencies_ready: z.boolean().describe('Detect if finished identifying additional dependencies.'),
  port: z.number().nullable().describe('Port number used by the resulted fragment. Null when no ports are exposed.'),
  // Multiple files support
  files: z.array(z.object({
    file_path: z.string().describe('Relative path to the file, including the file name.'),
    file_content: z.string().describe('Content of the file.'),
  })).describe('Array of files to create/modify. You can create multiple files, organize them in directories, and structure your project properly.'),
  // Legacy single file support (for backward compatibility)
  file_path: z.string().optional().describe('Legacy: Relative path to the file, including the file name. Use files array instead.'),
  code: z.string().optional().describe('Legacy: Code generated by the fragment. Use files array instead.'),
  // Terminal commands to execute after code generation
  terminal_commands: z.array(z.string()).optional().describe('Optional array of terminal/shell commands to execute in the sandbox after code generation. Use for setup, configuration, or running additional scripts.'),
  has_terminal_commands: z.boolean().describe('Detect if terminal commands need to be executed after code generation.'),
  // code: z.array(z.object({
  //   file_name: z.string().describe('Name of the file.'),
  //   file_path: z.string().describe('Relative path to the file, including the file name.'),
  //   file_content: z.string().describe('Content of the file.'),
  //   file_finished: z.boolean().describe('Detect if finished generating the file.'),
  // })),
  // code_finished: z.boolean().describe('Detect if finished generating the code.'),
  // error: z.string().optional().describe('Error message if the fragment is not valid.'),
})

export type FragmentSchema = z.infer<typeof fragmentSchema>
