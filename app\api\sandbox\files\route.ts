import { Sandbox } from '@e2b/code-interpreter'

export async function POST(req: Request) {
  const { sandboxId, path } = await req.json()

  try {
    const sandbox = await Sandbox.connect(sandboxId)
    const rawFiles = await sandbox.filesystem.list(path)

    // Transform the E2B filesystem entries to match our expected format
    const files = rawFiles.map((file: any) => ({
      path: `${path}/${file.name}`.replace(/\/+/g, '/'), // Clean up double slashes
      name: file.name,
      type: file.isDir ? 'directory' : 'file'
    }))

    return new Response(JSON.stringify({
      files
    }), {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('File system error:', error)
    return new Response(JSON.stringify({
      error: `Error accessing file system: ${error instanceof Error ? error.message : String(error)}`,
      files: [] // Provide empty array as fallback
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}