import { Sandbox } from '@e2b/code-interpreter'

export async function POST(req: Request) {
  const { sandboxId, path } = await req.json()

  try {
    const sandbox = await Sandbox.connect(sandboxId)

    // Use the correct E2B API method for listing files
    const rawFiles = await sandbox.files.list(path)
    console.log('Raw files from E2B:', rawFiles) // Debug log

    // Transform the E2B filesystem entries to match our expected format
    // E2B returns objects with properties like: { name, isDir, ... }
    const files = rawFiles.map((file: any) => {
      const filePath = path === '/' ? `/${file.name}` : `${path}/${file.name}`.replace(/\/+/g, '/')
      return {
        path: filePath,
        name: file.name,
        type: file.isDir ? 'directory' : 'file'
      }
    })

    console.log('Transformed files:', files) // Debug log

    return new Response(JSON.stringify({
      files
    }), {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('File system error:', error)
    return new Response(JSON.stringify({
      error: `Error accessing file system: ${error instanceof Error ? error.message : String(error)}`,
      files: [] // Provide empty array as fallback
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}